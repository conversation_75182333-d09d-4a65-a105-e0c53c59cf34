# 家庭健康管理系统数据库设计

## 1. 业务分析

### 核心实体识别
- **用户（User）**：应用的基础用户实体
- **家庭圈（Family）**：用户创建或加入的家庭组织
- **家庭成员（Family Member）**：家庭圈中的成员关系
- **健康档案（Health Profile）**：每个成员的基础健康信息
- **健康评估（Health Assessment）**：定期的健康评估记录

### 实体关系梳理
- 用户与家庭圈：多对多关系（一个用户可以属于多个家庭圈）
- 家庭圈与家庭成员：一对多关系
- 家庭成员与健康档案：一对一关系
- 家庭成员与健康评估：一对多关系

## 2. 表结构设计

### 2.1 用户表（rag_consumer）- 已存在

#### 现有表结构
```sql
CREATE TABLE `rag_consumer` (
  `id` bigint NOT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `country_code` varchar(10) NOT NULL DEFAULT '' COMMENT '手机号国家码 如"+86"',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮箱',
  `dingtalk_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `binding_user_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `app_user_id` varchar(20) DEFAULT NULL,
  `dingtalk_unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `business_id` varchar(255) DEFAULT NULL,
  `introduction` json DEFAULT NULL,
  `source` varchar(255) DEFAULT NULL COMMENT '0-钉钉应用,1-食神H5,2-魔盒,3-ROKiAPP,4-admin后台,5-老板电器小程序,6-技师助手,7-海外版食神',
  `extend_data` json DEFAULT NULL COMMENT '扩展数据',
  `guid` varchar(255) DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

#### 字段说明
- `id`：主键，用户唯一标识
- `nickname`：用户昵称
- `avatar`：头像URL
- `country_code`：手机号国家码
- `phone`：手机号
- `email`：邮箱地址
- `source`：用户来源渠道
- `extend_data`：扩展数据，使用JSON格式存储灵活信息

### 2.2 家庭圈表（families）

#### 表结构
```sql
CREATE TABLE `families` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '家庭圈ID',
  `name` varchar(100) NOT NULL COMMENT '家庭圈名称',
  `description` varchar(500) DEFAULT NULL COMMENT '家庭圈描述',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '家庭圈头像',
  `creator_id` bigint NOT NULL COMMENT '创建者ID，关联rag_consumer.id',
  `member_count` int unsigned NOT NULL DEFAULT '1' COMMENT '成员数量',
  `max_member_count` int unsigned NOT NULL DEFAULT '20' COMMENT '最大成员数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-禁用',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_families_creator` FOREIGN KEY (`creator_id`) REFERENCES `rag_consumer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭圈表';
```

#### 字段说明
- `id`：主键，家庭圈唯一标识
- `name`：家庭圈名称，如"我的家庭"
- `description`：家庭圈描述信息
- `creator_id`：创建者ID，关联现有用户表
- `member_count`：当前成员数量，冗余字段提升查询性能
- `max_member_count`：最大成员数量限制

## 3. 索引策略

### 用户表（rag_consumer）索引
- **主键索引**：`id`（聚簇索引）
- **建议添加索引**：
  - `phone`（登录查询优化）
  - `email`（邮箱登录查询）
  - `app_user_id`（应用用户查询）

### 家庭圈表（families）索引
- **主键索引**：`id`（聚簇索引）
- **普通索引**：
  - `creator_id`（创建者查询）
  - `status`（状态筛选）
  - `created_at`（时间排序）

### 家庭成员表（family_members）索引
- **主键索引**：`id`（聚簇索引）
- **唯一索引**：`uk_family_user`（family_id, user_id）防止重复加入
- **普通索引**：
  - `family_id`（家庭圈成员查询）
  - `user_id`（用户家庭查询）
  - `relationship`（关系筛选）
  - `role`（角色权限查询）
  - `status`（状态筛选）

## 4. SQL示例

### 创建家庭圈表
```sql
-- 家庭圈表创建语句（见上述表结构）
```

### 常用查询示例
```sql
-- 根据手机号查询用户
SELECT * FROM rag_consumer WHERE phone = '13800138000' AND is_deleted = 0;

-- 查询用户创建的家庭圈
SELECT f.* FROM families f
WHERE f.creator_id = 123456 AND f.is_deleted = 0
ORDER BY f.created_at DESC;

-- 查询家庭圈所有成员
SELECT fm.id, fm.member_name, fm.relationship, fm.gender, fm.role,
       rc.nickname, rc.avatar
FROM family_members fm
LEFT JOIN rag_consumer rc ON fm.user_id = rc.id
WHERE fm.family_id = 1 AND fm.is_deleted = 0
ORDER BY fm.role DESC, fm.join_time ASC;

-- 查询用户参与的所有家庭圈
SELECT f.id, f.name, f.avatar_url, fm.relationship, fm.role
FROM families f
INNER JOIN family_members fm ON f.id = fm.family_id
WHERE fm.user_id = 123456 AND f.is_deleted = 0 AND fm.is_deleted = 0
ORDER BY fm.join_time DESC;

-- 查询家庭圈中的管理员
SELECT fm.*, rc.nickname, rc.phone
FROM family_members fm
LEFT JOIN rag_consumer rc ON fm.user_id = rc.id
WHERE fm.family_id = 1 AND fm.role >= 2 AND fm.is_deleted = 0;

-- 统计各家庭圈的成员数量
SELECT f.id, f.name, COUNT(fm.id) as actual_member_count, f.member_count
FROM families f
LEFT JOIN family_members fm ON f.id = fm.family_id AND fm.is_deleted = 0
WHERE f.is_deleted = 0
GROUP BY f.id, f.name, f.member_count;
```

## 5. 扩展性考虑

### 用户表（rag_consumer）扩展性
- **多渠道支持**：已支持钉钉、H5、小程序等多种来源
- **扩展数据**：使用JSON字段存储灵活的扩展信息
- **国际化支持**：支持国家码，便于海外用户接入
- **第三方集成**：已预留钉钉等第三方平台集成字段

### 家庭圈表扩展性
- **成员管理**：支持最大成员数量限制和动态调整
- **权限扩展**：预留状态字段支持更多权限控制
- **多媒体支持**：支持家庭圈头像等多媒体内容
- **描述信息**：支持家庭圈详细描述和个性化设置

### 家庭成员表扩展性
- **虚拟成员支持**：user_id可为空，支持添加未注册的家庭成员
- **完整健康档案**：集成基础健康信息（身高、体重、健康问题等）
- **生活习惯记录**：支持睡眠、运动等生活习惯数据
- **特殊状态管理**：支持孕期、哺乳期等特殊时期标记
- **灵活关系定义**：relationship字段支持自定义家庭关系
- **角色权限管理**：支持多级权限控制（普通成员、管理员、创建者）
- **多家庭支持**：一个用户可以属于多个家庭圈

### 性能优化预留
- 使用bigint主键支持海量数据
- 合理的索引设计平衡查询性能和存储空间
- 软删除机制保证数据安全性
- 外键约束保证数据完整性

### 2.3 家庭成员表（family_members）

#### 表结构
```sql
CREATE TABLE `family_members` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '家庭成员ID',
  `family_id` bigint unsigned NOT NULL COMMENT '家庭圈ID，关联families.id',
  `user_id` bigint DEFAULT NULL COMMENT '关联用户ID，关联rag_consumer.id，可为空（虚拟成员）',
  `member_name` varchar(50) NOT NULL COMMENT '成员姓名/昵称',
  `member_avatar` varchar(500) DEFAULT NULL COMMENT '成员头像',
  `relationship` varchar(20) NOT NULL COMMENT '家庭关系：本人、爸爸、妈妈、儿子、女儿、爷爷、奶奶、外公、外婆等',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `height` varchar(20) DEFAULT NULL COMMENT '身高，如"156cm"',
  `weight` varchar(20) DEFAULT NULL COMMENT '体重，如"56kg"',
  `special_period` varchar(100) DEFAULT NULL COMMENT '特殊时期（如孕期、哺乳期等）',
  `health_issues` text DEFAULT NULL COMMENT '健康问题描述',
  `sleep_quality` tinyint DEFAULT NULL COMMENT '近期睡眠情况：1-很差，2-较差，3-一般，4-良好，5-很好',
  `exercise_intensity` tinyint DEFAULT NULL COMMENT '日常运动强度：1-从不，2-轻体力，3-中等，4-高强度',
  `is_primary` tinyint NOT NULL DEFAULT '0' COMMENT '是否为主要成员：0-否，1-是',
  `role` tinyint NOT NULL DEFAULT '1' COMMENT '角色：1-普通成员，2-管理员，3-创建者',
  `join_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '加入时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-禁用',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_family_user` (`family_id`, `user_id`),
  KEY `idx_family_id` (`family_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_relationship` (`relationship`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_family_members_family` FOREIGN KEY (`family_id`) REFERENCES `families` (`id`),
  CONSTRAINT `fk_family_members_user` FOREIGN KEY (`user_id`) REFERENCES `rag_consumer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭成员表';
```

#### 字段说明
- `id`：主键，家庭成员唯一标识
- `family_id`：所属家庭圈ID
- `user_id`：关联的应用用户ID，可为空（支持虚拟成员）
- `member_name`：成员姓名/昵称，如"超级爱甜食"、"橘子钙片"
- `relationship`：家庭关系，如"本人"、"爸爸"、"妈妈"等
- `height/weight`：身高体重等基础健康指标
- `special_period`：特殊时期，如孕期、哺乳期等
- `health_issues`：健康问题描述，支持长文本
- `sleep_quality`：近期睡眠情况评分
- `exercise_intensity`：日常运动强度等级
- `is_primary`：是否为主要成员（创建家庭圈的用户）
- `role`：成员角色，支持权限管理

## 6. 下一步设计计划

### 2.4 健康档案表（health_profiles）

#### 表结构
```sql
CREATE TABLE `health_profiles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `consumer_id` bigint NOT NULL COMMENT '档案所有者ID，关联rag_consumer表',
  `profile_name` varchar(50) NOT NULL COMMENT '档案名称，如"爸爸"、"妈妈"、"我"',
  `relationship` varchar(20) DEFAULT NULL COMMENT '与档案所有者的关系，如"本人"、"父亲"、"母亲"',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女，0-未知',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `height` varchar(20) DEFAULT NULL COMMENT '身高，如"170cm"',
  `weight` varchar(20) DEFAULT NULL COMMENT '体重，如"65kg"',
  `special_period` varchar(100) DEFAULT NULL COMMENT '特殊时期，如"孕期"、"哺乳期"',
  `health_issues` text DEFAULT NULL COMMENT '健康问题描述',
  `work_type` varchar(50) DEFAULT NULL COMMENT '工作类型，如"办公室文员"、"医生"、"教师"、"工人"等',
  `work_intensity` tinyint DEFAULT NULL COMMENT '工作强度：1-轻松，2-一般，3-繁忙，4-高压',
  `work_schedule` varchar(30) DEFAULT NULL COMMENT '工作时间类型：日班、夜班、轮班、弹性工作等',
  `sleep_quality` tinyint DEFAULT NULL COMMENT '睡眠质量评分：1-5分',
  `exercise_intensity` tinyint DEFAULT NULL COMMENT '运动强度：1-低，2-中，3-高',
  -- 标准字段
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_consumer_id` (`consumer_id`) COMMENT '档案所有者索引',
  KEY `idx_consumer_deleted` (`consumer_id`, `is_deleted`) COMMENT '查询用户档案的复合索引',
  CONSTRAINT `fk_health_profiles_consumer` FOREIGN KEY (`consumer_id`) REFERENCES `rag_consumer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康档案表 - 存储用户及其家庭成员的健康档案信息';
```

#### 字段说明
- `id`：主键，健康档案唯一标识
- `consumer_id`：档案所有者ID，关联现有用户表
- `profile_name`：档案名称，便于用户识别和管理
- `relationship`：与档案所有者的关系，支持家庭成员管理
- `avatar`：档案头像，支持个性化展示
- `gender`：性别信息，用于健康建议的个性化
- `birth_date`：出生日期，用于年龄计算和生命周期健康管理
- `height/weight`：基础体征数据，健康评估的重要指标
- `special_period`：特殊时期标记，如孕期、哺乳期等，影响健康建议
- `health_issues`：健康问题描述，支持详细的健康状况记录
- `work_type`：工作类型，用于职业健康风险评估和个性化建议
- `work_intensity`：工作强度等级，影响疲劳程度和恢复建议
- `work_schedule`：工作时间类型，影响作息和营养建议
- `sleep_quality`：睡眠质量评分，生活习惯的重要指标
- `exercise_intensity`：运动强度等级，用于运动建议和健康评估

### 待设计表结构
1. **健康评估表（health_assessments）** - 定期健康评估记录
2. **健康指标表（health_metrics）** - 具体健康指标数据

---

**状态：已完成用户表分析、家庭圈表、家庭成员表和健康档案表设计，等待审查确认后继续设计健康评估表**
